#!/usr/bin/env python3
"""
BATCH FILE MOVER - ULTRA FAST
Moves files to modular structure in seconds
"""
import os
import shutil
import glob
from pathlib import Path
import json

def move_files_batch():
    """Move files in batches based on patterns"""
    
    # Define batch move operations
    batch_operations = [
        # Core files
        {
            'pattern': 'js/auth*.js',
            'destination': 'src/core/auth/',
            'description': 'Moving auth files'
        },
        {
            'pattern': 'js/firebase*.js',
            'destination': 'src/core/config/',
            'description': 'Moving Firebase config files'
        },
        {
            'pattern': 'js/firestore*.js',
            'destination': 'src/core/storage/',
            'description': 'Moving Firestore files'
        },
        {
            'pattern': 'js/*storage*.js',
            'destination': 'src/core/storage/',
            'description': 'Moving storage files'
        },
        {
            'pattern': 'js/indexedDB.js',
            'destination': 'src/core/storage/',
            'description': 'Moving IndexedDB'
        },
        
        # Task management
        {
            'pattern': 'js/*task*.js',
            'destination': 'src/modules/tasks/services/',
            'description': 'Moving task files'
        },
        {
            'pattern': 'css/*task*.css',
            'destination': 'src/modules/tasks/components/',
            'description': 'Moving task CSS'
        },
        {
            'pattern': 'tasks.html',
            'destination': 'src/pages/',
            'description': 'Moving tasks page'
        },
        
        # Calendar & Schedule
        {
            'pattern': 'js/*calendar*.js',
            'destination': 'src/modules/calendar/services/',
            'description': 'Moving calendar files'
        },
        {
            'pattern': 'js/*schedule*.js',
            'destination': 'src/modules/calendar/services/',
            'description': 'Moving schedule files'
        },
        {
            'pattern': 'js/*timetable*.js',
            'destination': 'src/modules/calendar/services/',
            'description': 'Moving timetable files'
        },
        {
            'pattern': 'daily-calendar.html',
            'destination': 'src/pages/',
            'description': 'Moving calendar page'
        },
        {
            'pattern': 'css/*calendar*.css',
            'destination': 'src/modules/calendar/components/',
            'description': 'Moving calendar CSS'
        },
        
        # Study Spaces
        {
            'pattern': 'js/*study*.js',
            'destination': 'src/modules/study-spaces/services/',
            'description': 'Moving study space files'
        },
        {
            'pattern': 'study-spaces.html',
            'destination': 'src/pages/',
            'description': 'Moving study spaces page'
        },
        {
            'pattern': 'css/*study*.css',
            'destination': 'src/modules/study-spaces/components/',
            'description': 'Moving study spaces CSS'
        },
        
        # Flashcards
        {
            'pattern': 'js/*flashcard*.js',
            'destination': 'src/modules/flashcards/services/',
            'description': 'Moving flashcard files'
        },
        {
            'pattern': 'flashcards.html',
            'destination': 'src/pages/',
            'description': 'Moving flashcards page'
        },
        {
            'pattern': 'css/*flashcard*.css',
            'destination': 'src/modules/flashcards/components/',
            'description': 'Moving flashcard CSS'
        },
        
        # Workspace
        {
            'pattern': 'js/*workspace*.js',
            'destination': 'src/modules/workspace/services/',
            'description': 'Moving workspace files'
        },
        {
            'pattern': 'workspace.html',
            'destination': 'src/pages/',
            'description': 'Moving workspace page'
        },
        {
            'pattern': 'css/*workspace*.css',
            'destination': 'src/modules/workspace/components/',
            'description': 'Moving workspace CSS'
        },
        
        # Alarms
        {
            'pattern': 'js/*alarm*.js',
            'destination': 'src/modules/alarms/services/',
            'description': 'Moving alarm files'
        },
        {
            'pattern': 'css/*alarm*.css',
            'destination': 'src/modules/alarms/components/',
            'description': 'Moving alarm CSS'
        },
        
        # AI & Research
        {
            'pattern': 'js/ai-*.js',
            'destination': 'src/modules/ai/services/',
            'description': 'Moving AI files'
        },
        {
            'pattern': 'js/*gemini*.js',
            'destination': 'src/modules/ai/services/',
            'description': 'Moving Gemini files'
        },
        {
            'pattern': 'js/imageAnalyzer.js',
            'destination': 'src/modules/ai/services/',
            'description': 'Moving image analyzer'
        },
        
        # Priority & Calculations
        {
            'pattern': 'js/*priority*.js',
            'destination': 'src/modules/priority/services/',
            'description': 'Moving priority files'
        },
        {
            'pattern': 'priority-*.html',
            'destination': 'src/pages/',
            'description': 'Moving priority pages'
        },
        {
            'pattern': 'css/*priority*.css',
            'destination': 'src/modules/priority/components/',
            'description': 'Moving priority CSS'
        },
        
        # Subjects & Marks
        {
            'pattern': 'js/*subject*.js',
            'destination': 'src/modules/subjects/services/',
            'description': 'Moving subject files'
        },
        {
            'pattern': 'js/*marks*.js',
            'destination': 'src/modules/subjects/services/',
            'description': 'Moving marks files'
        },
        {
            'pattern': 'subject-marks.html',
            'destination': 'src/pages/',
            'description': 'Moving subject marks page'
        },
        {
            'pattern': 'css/*subject*.css',
            'destination': 'src/modules/subjects/components/',
            'description': 'Moving subject CSS'
        },
        
        # Sleep Management
        {
            'pattern': 'js/*sleep*.js',
            'destination': 'src/modules/sleep/services/',
            'description': 'Moving sleep files'
        },
        {
            'pattern': 'sleep-*.html',
            'destination': 'src/pages/',
            'description': 'Moving sleep pages'
        },
        {
            'pattern': 'css/*sleep*.css',
            'destination': 'src/modules/sleep/components/',
            'description': 'Moving sleep CSS'
        },
        
        # Shared utilities
        {
            'pattern': 'js/common*.js',
            'destination': 'src/shared/utils/',
            'description': 'Moving common utilities'
        },
        {
            'pattern': 'js/ui-utilities.js',
            'destination': 'src/shared/utils/',
            'description': 'Moving UI utilities'
        },
        {
            'pattern': 'js/*theme*.js',
            'destination': 'src/shared/utils/',
            'description': 'Moving theme files'
        },
        {
            'pattern': 'js/cross-tab-sync.js',
            'destination': 'src/shared/utils/',
            'description': 'Moving cross-tab sync'
        }
    ]
    
    moved_files = []
    
    for operation in batch_operations:
        print(f"📁 {operation['description']}...")
        
        # Find matching files
        files = glob.glob(operation['pattern'])
        
        if not files:
            print(f"   ⚠️  No files found for pattern: {operation['pattern']}")
            continue
        
        # Ensure destination exists
        dest_path = Path(operation['destination'])
        dest_path.mkdir(parents=True, exist_ok=True)
        
        # Move files
        for file_path in files:
            try:
                file_name = Path(file_path).name
                dest_file = dest_path / file_name
                
                # Avoid overwriting
                if dest_file.exists():
                    print(f"   ⚠️  Skipping {file_path} (already exists)")
                    continue
                
                shutil.move(file_path, dest_file)
                moved_files.append({
                    'from': file_path,
                    'to': str(dest_file)
                })
                print(f"   ✅ Moved {file_path} -> {dest_file}")
                
            except Exception as e:
                print(f"   ❌ Error moving {file_path}: {e}")
    
    # Save log of moved files
    with open('moved_files_log.json', 'w') as f:
        json.dump(moved_files, f, indent=2)
    
    print(f"\n🎉 Batch move complete! Moved {len(moved_files)} files.")
    print("📄 Check moved_files_log.json for details.")

if __name__ == "__main__":
    print("🚀 Starting batch file move...")
    move_files_batch()
