#!/usr/bin/env python3
"""
IMPORT UPDATER - ULTRA FAST
Updates all import statements to use new modular paths
"""
import os
import re
from pathlib import Path
import json

def update_imports_batch():
    """Update import statements in all files"""
    
    # Import mapping rules
    import_mappings = {
        # Core modules
        r'js/auth\.js': 'src/core/auth/auth.js',
        r'js/firebase-config\.js': 'src/core/config/firebase-config.js',
        r'js/firestore\.js': 'src/core/storage/firestore.js',
        r'js/indexedDB\.js': 'src/core/storage/indexedDB.js',
        r'js/storageManager\.js': 'src/core/storage/storageManager.js',
        
        # Task management
        r'js/tasksManager\.js': 'src/modules/tasks/services/tasksManager.js',
        r'js/currentTaskManager\.js': 'src/modules/tasks/services/currentTaskManager.js',
        r'js/taskFilters\.js': 'src/modules/tasks/utils/taskFilters.js',
        r'js/taskLinks\.js': 'src/modules/tasks/components/taskLinks.js',
        r'js/task-notes\.js': 'src/modules/tasks/components/task-notes.js',
        r'js/taskAttachments\.js': 'src/modules/tasks/services/taskAttachments.js',
        
        # Calendar
        r'js/calendarManager\.js': 'src/modules/calendar/services/calendarManager.js',
        r'js/scheduleManager\.js': 'src/modules/calendar/services/scheduleManager.js',
        r'js/timetableAnalyzer\.js': 'src/modules/calendar/services/timetableAnalyzer.js',
        
        # Study spaces
        r'js/studySpacesManager\.js': 'src/modules/study-spaces/services/studySpacesManager.js',
        r'js/studySpaceAnalyzer\.js': 'src/modules/study-spaces/services/studySpaceAnalyzer.js',
        
        # Flashcards
        r'js/flashcardManager\.js': 'src/modules/flashcards/services/flashcardManager.js',
        
        # Workspace
        r'js/workspace-core\.js': 'src/modules/workspace/services/workspace-core.js',
        r'js/workspace-ui\.js': 'src/modules/workspace/components/workspace-ui.js',
        
        # Alarms
        r'js/alarm-service\.js': 'src/modules/alarms/services/alarm-service.js',
        r'js/alarm-handler\.js': 'src/modules/alarms/services/alarm-handler.js',
        
        # AI
        r'js/gemini-api\.js': 'src/modules/ai/services/gemini-api.js',
        r'js/imageAnalyzer\.js': 'src/modules/ai/services/imageAnalyzer.js',
        
        # Priority
        r'js/priority-list-sorting\.js': 'src/modules/priority/services/priority-list-sorting.js',
        
        # Subjects
        r'js/subject-management\.js': 'src/modules/subjects/services/subject-management.js',
        r'js/subject-marks\.js': 'src/modules/subjects/services/subject-marks.js',
        
        # Sleep
        r'js/sleepScheduleManager\.js': 'src/modules/sleep/services/sleepScheduleManager.js',
        
        # Shared
        r'js/common\.js': 'src/shared/utils/common.js',
        r'js/ui-utilities\.js': 'src/shared/utils/ui-utilities.js',
        r'js/themeManager\.js': 'src/shared/utils/themeManager.js',
        r'js/cross-tab-sync\.js': 'src/shared/utils/cross-tab-sync.js'
    }
    
    # CSS mapping rules
    css_mappings = {
        r'css/task-display\.css': 'src/modules/tasks/components/task-display.css',
        r'css/daily-calendar\.css': 'src/modules/calendar/components/daily-calendar.css',
        r'css/study-spaces\.css': 'src/modules/study-spaces/components/study-spaces.css',
        r'css/flashcards\.css': 'src/modules/flashcards/components/flashcards.css',
        r'css/workspace\.css': 'src/modules/workspace/components/workspace.css',
        r'css/alarm-service\.css': 'src/modules/alarms/components/alarm-service.css',
        r'css/priority-calculator\.css': 'src/modules/priority/components/priority-calculator.css',
        r'css/subject-marks\.css': 'src/modules/subjects/components/subject-marks.css',
        r'css/sleep-saboteurs\.css': 'src/modules/sleep/components/sleep-saboteurs.css'
    }
    
    def update_file_imports(file_path):
        """Update imports in a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Update JS imports
            for old_pattern, new_path in import_mappings.items():
                # Handle different import patterns
                patterns = [
                    rf'import\s+.*?\s+from\s+[\'"]\.?/?{old_pattern}[\'"]',
                    rf'import\s+[\'"]\.?/?{old_pattern}[\'"]',
                    rf'<script\s+src=[\'"]\.?/?{old_pattern}[\'"]',
                    rf'src=[\'"]\.?/?{old_pattern}[\'"]'
                ]
                
                for pattern in patterns:
                    content = re.sub(pattern, lambda m: m.group(0).replace(old_pattern.replace('\\', ''), new_path), content)
            
            # Update CSS imports
            for old_pattern, new_path in css_mappings.items():
                patterns = [
                    rf'<link\s+.*?href=[\'"]\.?/?{old_pattern}[\'"]',
                    rf'href=[\'"]\.?/?{old_pattern}[\'"]',
                    rf'@import\s+[\'"]\.?/?{old_pattern}[\'"]'
                ]
                
                for pattern in patterns:
                    content = re.sub(pattern, lambda m: m.group(0).replace(old_pattern.replace('\\', ''), new_path), content)
            
            # Write back if changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error updating {file_path}: {e}")
            return False
    
    # Find all files to update
    file_patterns = ['**/*.html', '**/*.js', '**/*.css']
    updated_files = []
    
    for pattern in file_patterns:
        for file_path in Path('.').glob(pattern):
            # Skip node_modules and other directories
            if any(part in str(file_path) for part in ['node_modules', '.git', 'dist', 'build']):
                continue
            
            if update_file_imports(file_path):
                updated_files.append(str(file_path))
                print(f"✅ Updated imports in {file_path}")
    
    print(f"\n🎉 Updated imports in {len(updated_files)} files!")
    
    # Save log
    with open('updated_imports_log.json', 'w') as f:
        json.dump(updated_files, f, indent=2)

def create_import_aliases():
    """Create import alias configuration"""
    
    aliases = {
        "@core": "./src/core",
        "@modules": "./src/modules",
        "@shared": "./src/shared",
        "@assets": "./src/assets",
        "@pages": "./src/pages",
        "@tasks": "./src/modules/tasks",
        "@calendar": "./src/modules/calendar",
        "@study-spaces": "./src/modules/study-spaces",
        "@flashcards": "./src/modules/flashcards",
        "@workspace": "./src/modules/workspace",
        "@alarms": "./src/modules/alarms",
        "@ai": "./src/modules/ai",
        "@priority": "./src/modules/priority",
        "@subjects": "./src/modules/subjects",
        "@sleep": "./src/modules/sleep"
    }
    
    # Create jsconfig.json for VS Code
    jsconfig = {
        "compilerOptions": {
            "baseUrl": ".",
            "paths": {f"{alias}/*": [f"{path}/*"] for alias, path in aliases.items()}
        },
        "exclude": ["node_modules", "dist", "build"]
    }
    
    with open('jsconfig.json', 'w') as f:
        json.dump(jsconfig, f, indent=2)
    
    print("✅ Import aliases created!")

if __name__ == "__main__":
    print("🚀 Updating imports...")
    update_imports_batch()
    create_import_aliases()
    print("🎉 Import updates complete!")
