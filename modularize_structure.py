#!/usr/bin/env python3
"""
ULTRA-FAST MODULARIZATION SCRIPT
Creates modular structure in 30 seconds
"""
import os
import shutil
import json
from pathlib import Path

def create_modular_structure():
    """Create the new modular directory structure"""
    
    # Define the new modular structure
    structure = {
        'src': {
            'core': ['auth', 'config', 'storage', 'api'],
            'modules': {
                'tasks': ['components', 'services', 'utils'],
                'calendar': ['components', 'services', 'utils'],
                'study-spaces': ['components', 'services', 'utils'],
                'flashcards': ['components', 'services', 'utils'],
                'workspace': ['components', 'services', 'utils'],
                'alarms': ['components', 'services', 'utils'],
                'ai': ['components', 'services', 'utils'],
                'priority': ['components', 'services', 'utils'],
                'subjects': ['components', 'services', 'utils'],
                'sleep': ['components', 'services', 'utils']
            },
            'shared': ['components', 'utils', 'constants', 'types'],
            'assets': ['css', 'js', 'images', 'sounds'],
            'pages': []
        },
        'dist': [],
        'docs': [],
        'tests': ['unit', 'integration', 'e2e']
    }
    
    def create_dirs(base_path, structure_dict):
        for key, value in structure_dict.items():
            current_path = base_path / key
            current_path.mkdir(exist_ok=True)
            
            if isinstance(value, dict):
                create_dirs(current_path, value)
            elif isinstance(value, list):
                for item in value:
                    (current_path / item).mkdir(exist_ok=True)
    
    # Create the structure
    base_path = Path('.')
    create_dirs(base_path, structure)
    
    # Create index files for each module
    modules_path = Path('src/modules')
    for module in modules_path.iterdir():
        if module.is_dir():
            # Create module index
            (module / 'index.js').touch()
            # Create README
            with open(module / 'README.md', 'w') as f:
                f.write(f"# {module.name.title()} Module\n\nModule for {module.name} functionality.\n")
    
    print("✅ Modular structure created!")

def create_file_mapping():
    """Create mapping of current files to new structure"""
    
    mapping = {
        # Core files
        'js/auth.js': 'src/core/auth/auth.js',
        'js/firebase-*.js': 'src/core/config/',
        'js/firestore*.js': 'src/core/storage/',
        'js/indexedDB.js': 'src/core/storage/indexedDB.js',
        'js/storageManager.js': 'src/core/storage/storageManager.js',
        
        # Task management
        'js/tasksManager.js': 'src/modules/tasks/services/tasksManager.js',
        'js/currentTaskManager.js': 'src/modules/tasks/services/currentTaskManager.js',
        'js/taskFilters.js': 'src/modules/tasks/utils/taskFilters.js',
        'js/taskLinks.js': 'src/modules/tasks/components/taskLinks.js',
        'js/task-notes*.js': 'src/modules/tasks/components/',
        'js/taskAttachments.js': 'src/modules/tasks/services/taskAttachments.js',
        'tasks.html': 'src/pages/tasks.html',
        'css/task*.css': 'src/modules/tasks/components/',
        
        # Calendar & Schedule
        'js/calendar*.js': 'src/modules/calendar/services/',
        'js/scheduleManager.js': 'src/modules/calendar/services/scheduleManager.js',
        'js/timetable*.js': 'src/modules/calendar/services/',
        'daily-calendar.html': 'src/pages/daily-calendar.html',
        'css/daily-calendar.css': 'src/modules/calendar/components/daily-calendar.css',
        
        # Study Spaces
        'js/studySpaces*.js': 'src/modules/study-spaces/services/',
        'study-spaces.html': 'src/pages/study-spaces.html',
        'css/study-spaces.css': 'src/modules/study-spaces/components/study-spaces.css',
        
        # Flashcards
        'js/flashcard*.js': 'src/modules/flashcards/services/',
        'flashcards.html': 'src/pages/flashcards.html',
        'css/flashcards.css': 'src/modules/flashcards/components/flashcards.css',
        
        # Workspace
        'js/workspace*.js': 'src/modules/workspace/services/',
        'workspace.html': 'src/pages/workspace.html',
        'css/workspace.css': 'src/modules/workspace/components/workspace.css',
        
        # Alarms
        'js/alarm*.js': 'src/modules/alarms/services/',
        'css/alarm*.css': 'src/modules/alarms/components/',
        
        # AI & Research
        'js/ai-*.js': 'src/modules/ai/services/',
        'js/gemini*.js': 'src/modules/ai/services/',
        'js/imageAnalyzer.js': 'src/modules/ai/services/imageAnalyzer.js',
        
        # Priority & Calculations
        'js/priority*.js': 'src/modules/priority/services/',
        'priority-*.html': 'src/pages/',
        'css/priority*.css': 'src/modules/priority/components/',
        
        # Subjects & Marks
        'js/subject*.js': 'src/modules/subjects/services/',
        'js/marks*.js': 'src/modules/subjects/services/',
        'subject-marks.html': 'src/pages/subject-marks.html',
        'css/subject*.css': 'src/modules/subjects/components/',
        
        # Sleep Management
        'js/sleep*.js': 'src/modules/sleep/services/',
        'sleep-saboteurs.html': 'src/pages/sleep-saboteurs.html',
        'css/sleep*.css': 'src/modules/sleep/components/',
        
        # Shared utilities
        'js/common*.js': 'src/shared/utils/',
        'js/ui-utilities.js': 'src/shared/utils/ui-utilities.js',
        'js/theme*.js': 'src/shared/utils/',
        'js/cross-tab-sync.js': 'src/shared/utils/cross-tab-sync.js',
        
        # Assets
        'css/': 'src/assets/css/',
        'assets/': 'src/assets/',
        'sounds/': 'src/assets/sounds/',
        'alarm-sounds/': 'src/assets/sounds/alarms/',
    }
    
    # Save mapping to file
    with open('file_mapping.json', 'w') as f:
        json.dump(mapping, f, indent=2)
    
    print("✅ File mapping created!")

if __name__ == "__main__":
    print("🚀 Starting ultra-fast modularization...")
    create_modular_structure()
    create_file_mapping()
    print("🎉 Structure ready! Run the batch move script next.")
