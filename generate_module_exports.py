#!/usr/bin/env python3
"""
MODULE EXPORT GENERATOR
Creates index.js files with proper exports for each module
"""
import os
from pathlib import Path
import json

def generate_module_exports():
    """Generate index.js files for all modules"""
    
    modules_path = Path('src/modules')
    
    # Module configurations
    module_configs = {
        'tasks': {
            'services': ['tasksManager', 'currentTaskManager', 'taskAttachments'],
            'components': ['taskLinks', 'taskNotes'],
            'utils': ['taskFilters']
        },
        'calendar': {
            'services': ['calendarManager', 'scheduleManager', 'timetableAnalyzer', 'timetableIntegration'],
            'components': ['calendarViews'],
            'utils': []
        },
        'study-spaces': {
            'services': ['studySpacesManager', 'studySpacesFirestore', 'studySpaceAnalyzer'],
            'components': [],
            'utils': []
        },
        'flashcards': {
            'services': ['flashcardManager', 'flashcardTaskIntegration'],
            'components': [],
            'utils': []
        },
        'workspace': {
            'services': ['workspaceCore', 'workspaceDocument', 'workspaceFormatting', 'workspaceMedia', 'workspaceTablesLinks', 'workspaceAttachments'],
            'components': ['workspaceUI'],
            'utils': []
        },
        'alarms': {
            'services': ['alarmService', 'alarmHandler', 'alarmDataService'],
            'components': ['alarmMiniDisplay'],
            'utils': []
        },
        'ai': {
            'services': ['geminiApi', 'googleGenerativeAI', 'imageAnalyzer', 'aiLatexConversion', 'aiResearcher'],
            'components': [],
            'utils': []
        },
        'priority': {
            'services': ['priorityListSorting', 'priorityListUtils', 'prioritySyncFix', 'priorityWorkerWrapper'],
            'components': [],
            'utils': []
        },
        'subjects': {
            'services': ['subjectManagement', 'subjectMarks', 'subjectMarksIntegration', 'marksTracking'],
            'components': ['subjectMarksUI'],
            'utils': []
        },
        'sleep': {
            'services': ['sleepScheduleManager', 'sleepTimeCalculator', 'sleepSaboteursInit'],
            'components': [],
            'utils': []
        }
    }
    
    for module_name, config in module_configs.items():
        module_path = modules_path / module_name
        
        if not module_path.exists():
            continue
        
        # Generate main module index
        index_content = f"""// {module_name.title()} Module
// Auto-generated exports

"""
        
        # Add exports for each category
        for category, files in config.items():
            if files:
                index_content += f"// {category.title()}\n"
                for file_name in files:
                    # Convert camelCase to kebab-case for file names
                    file_kebab = ''.join(['-' + c.lower() if c.isupper() else c for c in file_name]).lstrip('-')
                    index_content += f"export {{ default as {file_name} }} from './{category}/{file_kebab}.js';\n"
                index_content += "\n"
        
        # Add barrel export
        index_content += f"""
// Barrel export for {module_name}
export * from './services/index.js';
export * from './components/index.js';
export * from './utils/index.js';
"""
        
        # Write main index file
        with open(module_path / 'index.js', 'w') as f:
            f.write(index_content)
        
        # Generate category index files
        for category in ['services', 'components', 'utils']:
            category_path = module_path / category
            if category_path.exists():
                category_index = f"// {category.title()} for {module_name}\n\n"
                
                # Find all JS files in category
                js_files = list(category_path.glob('*.js'))
                for js_file in js_files:
                    if js_file.name != 'index.js':
                        file_name = js_file.stem
                        # Convert to camelCase
                        camel_name = ''.join(word.capitalize() for word in file_name.split('-'))
                        category_index += f"export {{ default as {camel_name} }} from './{js_file.name}';\n"
                
                with open(category_path / 'index.js', 'w') as f:
                    f.write(category_index)
    
    print("✅ Module exports generated!")

def generate_main_app_index():
    """Generate main app index.js"""
    
    main_index = """// Main Application Entry Point
// Auto-generated module imports

// Core modules
import * as Auth from './core/auth/index.js';
import * as Config from './core/config/index.js';
import * as Storage from './core/storage/index.js';
import * as API from './core/api/index.js';

// Feature modules
import * as Tasks from './modules/tasks/index.js';
import * as Calendar from './modules/calendar/index.js';
import * as StudySpaces from './modules/study-spaces/index.js';
import * as Flashcards from './modules/flashcards/index.js';
import * as Workspace from './modules/workspace/index.js';
import * as Alarms from './modules/alarms/index.js';
import * as AI from './modules/ai/index.js';
import * as Priority from './modules/priority/index.js';
import * as Subjects from './modules/subjects/index.js';
import * as Sleep from './modules/sleep/index.js';

// Shared utilities
import * as Shared from './shared/index.js';

// Application class
class GPAceApp {
    constructor() {
        this.modules = {
            auth: Auth,
            config: Config,
            storage: Storage,
            api: API,
            tasks: Tasks,
            calendar: Calendar,
            studySpaces: StudySpaces,
            flashcards: Flashcards,
            workspace: Workspace,
            alarms: Alarms,
            ai: AI,
            priority: Priority,
            subjects: Subjects,
            sleep: Sleep,
            shared: Shared
        };
        
        this.initialized = false;
    }
    
    async init() {
        if (this.initialized) return;
        
        console.log('🚀 Initializing GPAce App...');
        
        // Initialize core modules first
        await this.initCore();
        
        // Initialize feature modules
        await this.initModules();
        
        this.initialized = true;
        console.log('✅ GPAce App initialized!');
    }
    
    async initCore() {
        // Initialize auth, config, storage, etc.
        console.log('🔧 Initializing core modules...');
    }
    
    async initModules() {
        // Initialize feature modules
        console.log('📦 Initializing feature modules...');
    }
    
    getModule(name) {
        return this.modules[name];
    }
}

// Export singleton instance
export default new GPAceApp();

// Export modules for direct access
export {
    Auth,
    Config,
    Storage,
    API,
    Tasks,
    Calendar,
    StudySpaces,
    Flashcards,
    Workspace,
    Alarms,
    AI,
    Priority,
    Subjects,
    Sleep,
    Shared
};
"""
    
    with open('src/index.js', 'w') as f:
        f.write(main_index)
    
    print("✅ Main app index generated!")

def generate_shared_exports():
    """Generate shared module exports"""
    
    shared_path = Path('src/shared')
    
    # Shared utilities index
    utils_index = """// Shared Utilities
export { default as uiUtilities } from './ui-utilities.js';
export { default as themeManager } from './theme-manager.js';
export { default as crossTabSync } from './cross-tab-sync.js';
export { default as common } from './common.js';
"""
    
    # Create shared index
    shared_index = """// Shared Module
export * from './utils/index.js';
export * from './components/index.js';
export * from './constants/index.js';
export * from './types/index.js';
"""
    
    # Write files
    (shared_path / 'utils').mkdir(exist_ok=True)
    with open(shared_path / 'utils' / 'index.js', 'w') as f:
        f.write(utils_index)
    
    with open(shared_path / 'index.js', 'w') as f:
        f.write(shared_index)
    
    print("✅ Shared exports generated!")

if __name__ == "__main__":
    print("🚀 Generating module exports...")
    generate_module_exports()
    generate_main_app_index()
    generate_shared_exports()
    print("🎉 All exports generated!")
